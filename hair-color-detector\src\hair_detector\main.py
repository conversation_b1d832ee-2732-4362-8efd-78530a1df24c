"""Main Hair Color Detection Utility

Command-line interface for detecting and categorizing hair colors in images.
"""

import click
import cv2
import os
import json
import shutil
from pathlib import Path
from typing import List, Dict, Any
import logging

from .detector import HairDetector
from .color_classifier import HairColorClassifier


def setup_logging(verbose: bool = False):
    """Setup logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


@click.group()
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
def cli(verbose):
    """Hair Color Detection Utility - Categorize images based on hair color."""
    setup_logging(verbose)


@cli.command()
@click.argument('image_path', type=click.Path(exists=True))
@click.option('--output', '-o', type=click.Path(), help='Output directory for results')
@click.option('--save-debug', is_flag=True, help='Save debug images showing detection results')
def analyze(image_path: str, output: str, save_debug: bool):
    """Analyze hair color in a single image."""
    
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        click.echo(f"Error: Could not load image {image_path}", err=True)
        return
    
    # Initialize detectors
    hair_detector = HairDetector()
    color_classifier = HairColorClassifier()
    
    # Detect hair
    hair_results = hair_detector.detect_hair(image)
    
    if not hair_results:
        click.echo("No faces or hair detected in the image.")
        return
    
    # Analyze each detected hair region
    results = []
    for i, (hair_mask, hair_region) in enumerate(hair_results):
        # Extract hair pixels
        hair_pixels = hair_detector.extract_hair_pixels(image, hair_mask, hair_region)
        
        if len(hair_pixels) == 0:
            click.echo(f"No hair pixels detected in region {i+1}")
            continue
        
        # Classify hair color
        color_analysis = color_classifier.analyze_hair_color_distribution(hair_pixels)
        
        results.append({
            'region_id': i + 1,
            'hair_region': hair_region,
            'color_analysis': color_analysis
        })
        
        # Print results
        click.echo(f"\nHair Region {i+1}:")
        click.echo(f"  Primary Color: {color_analysis['description']}")
        click.echo(f"  Confidence: {color_analysis['confidence']:.2%}")
        click.echo(f"  Hair Pixels: {color_analysis['total_pixels']}")
        
        if len(color_analysis['color_distribution']) > 1:
            click.echo("  Color Distribution:")
            for color, score in color_analysis['color_distribution'].items():
                if score > 0.1:  # Only show significant colors
                    desc = color_classifier.get_color_description(color)
                    click.echo(f"    {desc}: {score:.1%}")
    
    # Save results if output directory specified
    if output:
        output_dir = Path(output)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Save JSON results
        image_name = Path(image_path).stem
        results_file = output_dir / f"{image_name}_hair_analysis.json"
        
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        click.echo(f"\nResults saved to: {results_file}")
        
        # Save debug images if requested
        if save_debug:
            save_debug_images(image, hair_results, output_dir, image_name)


def save_debug_images(image, hair_results, output_dir: Path, image_name: str):
    """Save debug images showing detection results."""
    
    # Create debug image with face and hair regions marked
    debug_image = image.copy()
    
    for i, (hair_mask, hair_region) in enumerate(hair_results):
        x, y, w, h = hair_region
        
        # Draw hair region rectangle
        cv2.rectangle(debug_image, (x, y), (x + w, y + h), (0, 255, 0), 2)
        cv2.putText(debug_image, f"Hair {i+1}", (x, y-10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        # Save individual hair mask
        mask_file = output_dir / f"{image_name}_hair_mask_{i+1}.png"
        cv2.imwrite(str(mask_file), hair_mask)
    
    # Save debug image
    debug_file = output_dir / f"{image_name}_debug.png"
    cv2.imwrite(str(debug_file), debug_image)
    
    click.echo(f"Debug images saved to: {output_dir}")


@cli.command()
@click.argument('input_dir', type=click.Path(exists=True))
@click.argument('output_dir', type=click.Path())
@click.option('--organize', is_flag=True, help='Organize images into color-based subdirectories')
@click.option('--extensions', default='jpg,jpeg,png,bmp,tiff', help='Image file extensions to process')
def batch(input_dir: str, output_dir: str, organize: bool, extensions: str):
    """Process multiple images in a directory."""
    
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Get list of image files
    ext_list = [f".{ext.strip()}" for ext in extensions.split(',')]
    image_files = []
    
    for ext in ext_list:
        image_files.extend(input_path.glob(f"*{ext}"))
        image_files.extend(input_path.glob(f"*{ext.upper()}"))
    
    if not image_files:
        click.echo(f"No image files found in {input_dir}")
        return
    
    click.echo(f"Found {len(image_files)} images to process")
    
    # Initialize detectors
    hair_detector = HairDetector()
    color_classifier = HairColorClassifier()
    
    # Process each image
    batch_results = []
    
    with click.progressbar(image_files, label='Processing images') as bar:
        for image_file in bar:
            try:
                # Load image
                image = cv2.imread(str(image_file))
                if image is None:
                    continue
                
                # Detect and analyze hair
                hair_results = hair_detector.detect_hair(image)
                
                if not hair_results:
                    continue
                
                # Get primary hair color from first detected region
                hair_mask, hair_region = hair_results[0]
                hair_pixels = hair_detector.extract_hair_pixels(image, hair_mask, hair_region)
                
                if len(hair_pixels) == 0:
                    continue
                
                primary_color, confidence = color_classifier.get_primary_hair_color(hair_pixels)
                
                result = {
                    'filename': image_file.name,
                    'primary_color': primary_color,
                    'confidence': confidence,
                    'num_regions': len(hair_results)
                }
                
                batch_results.append(result)
                
                # Organize files if requested
                if organize:
                    organize_image(image_file, output_path, primary_color)
                
            except Exception as e:
                logging.error(f"Error processing {image_file}: {e}")
                continue
    
    # Save batch results
    results_file = output_path / "batch_results.json"
    with open(results_file, 'w') as f:
        json.dump(batch_results, f, indent=2)
    
    # Print summary
    color_counts = {}
    for result in batch_results:
        color = result['primary_color']
        color_counts[color] = color_counts.get(color, 0) + 1
    
    click.echo(f"\nProcessed {len(batch_results)} images successfully")
    click.echo("Hair color distribution:")
    for color, count in sorted(color_counts.items()):
        click.echo(f"  {color}: {count} images")
    
    click.echo(f"\nResults saved to: {results_file}")


def organize_image(image_file: Path, output_dir: Path, hair_color: str):
    """Organize image into color-based subdirectory."""
    
    color_dir = output_dir / hair_color
    color_dir.mkdir(exist_ok=True)
    
    dest_file = color_dir / image_file.name
    shutil.copy2(image_file, dest_file)


@cli.command()
@click.argument('image_path', type=click.Path(exists=True))
def demo(image_path: str):
    """Quick demo of hair detection on an image."""
    
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        click.echo(f"Error: Could not load image {image_path}", err=True)
        return
    
    # Initialize detectors
    hair_detector = HairDetector()
    color_classifier = HairColorClassifier()
    
    # Detect hair
    hair_results = hair_detector.detect_hair(image)
    
    if not hair_results:
        click.echo("No faces or hair detected in the image.")
        return
    
    click.echo(f"Detected {len(hair_results)} hair region(s)")
    
    for i, (hair_mask, hair_region) in enumerate(hair_results):
        hair_pixels = hair_detector.extract_hair_pixels(image, hair_mask, hair_region)
        
        if len(hair_pixels) > 0:
            primary_color, confidence = color_classifier.get_primary_hair_color(hair_pixels)
            description = color_classifier.get_color_description(primary_color)
            
            click.echo(f"Region {i+1}: {description} (confidence: {confidence:.1%})")


def main():
    """Entry point for the CLI."""
    cli()


if __name__ == '__main__':
    main()
