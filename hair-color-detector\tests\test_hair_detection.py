"""Tests for hair detection functionality."""

import pytest
import cv2
import numpy as np
from pathlib import Path
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from hair_detector.detector import HairDetector
from hair_detector.color_classifier import HairColorClassifier


class TestHairDetector:
    """Test cases for HairDetector class."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.detector = HairDetector()
        self.classifier = HairColorClassifier()
    
    def test_detector_initialization(self):
        """Test that detector initializes correctly."""
        assert self.detector is not None
        assert self.detector.face_cascade is not None
        assert not self.detector.face_cascade.empty()
    
    def test_classifier_initialization(self):
        """Test that classifier initializes correctly."""
        assert self.classifier is not None
        assert self.classifier.n_clusters == 3
        assert len(self.classifier.COLOR_RANGES) > 0
    
    def test_face_detection_with_dummy_image(self):
        """Test face detection with a simple test image."""
        # Create a simple test image (black square on white background)
        test_image = np.ones((300, 300, 3), dtype=np.uint8) * 255
        test_image[100:200, 100:200] = [0, 0, 0]  # Black square
        
        faces = self.detector.detect_faces(test_image)
        # This might not detect faces since it's just a black square
        assert isinstance(faces, list)
    
    def test_hair_region_estimation(self):
        """Test hair region estimation."""
        # Test with a mock face rectangle
        test_image = np.ones((400, 400, 3), dtype=np.uint8) * 255
        face_rect = (100, 150, 100, 120)  # x, y, w, h
        
        hair_region = self.detector.estimate_hair_region(test_image, face_rect)
        
        assert len(hair_region) == 4
        x, y, w, h = hair_region
        assert x >= 0 and y >= 0
        assert w > 0 and h > 0
        assert y < face_rect[1]  # Hair should be above face
    
    def test_color_classification(self):
        """Test color classification with known colors."""
        # Test black color
        black_color = np.array([0, 0, 0])
        black_category = self.classifier.classify_color(black_color)
        assert black_category == 'black'
        
        # Test white color
        white_color = np.array([255, 255, 255])
        white_category = self.classifier.classify_color(white_color)
        assert white_category in ['white', 'gray']
        
        # Test brown color
        brown_color = np.array([50, 30, 20])
        brown_category = self.classifier.classify_color(brown_color)
        assert brown_category in ['brown', 'black']
    
    def test_hair_color_analysis_empty_pixels(self):
        """Test hair color analysis with empty pixel array."""
        empty_pixels = np.array([]).reshape(0, 3)
        result = self.classifier.analyze_hair_color_distribution(empty_pixels)
        
        assert result['primary_color'] == 'unknown'
        assert result['confidence'] == 0.0
        assert result['total_pixels'] == 0
    
    def test_hair_color_analysis_with_pixels(self):
        """Test hair color analysis with sample pixels."""
        # Create sample hair pixels (dark brown)
        hair_pixels = np.array([
            [30, 20, 10],
            [35, 25, 15],
            [25, 15, 8],
            [40, 30, 20]
        ], dtype=np.uint8)
        
        result = self.classifier.analyze_hair_color_distribution(hair_pixels)
        
        assert result['primary_color'] in ['brown', 'black']
        assert result['confidence'] > 0
        assert result['total_pixels'] == 4
        assert 'color_distribution' in result
        assert 'dominant_colors' in result


def test_sample_image_processing():
    """Test processing of the sample image if it exists."""
    sample_image_path = Path(__file__).parent.parent / "sample_images" / "sample_image.png"
    
    if not sample_image_path.exists():
        pytest.skip("Sample image not found")
    
    # Load the sample image
    image = cv2.imread(str(sample_image_path))
    assert image is not None, "Could not load sample image"
    
    # Initialize detectors
    detector = HairDetector()
    classifier = HairColorClassifier()
    
    # Try to detect hair
    hair_results = detector.detect_hair(image)
    
    # The results depend on the image content, so we just check the structure
    assert isinstance(hair_results, list)
    
    # If hair is detected, test the classification
    for hair_mask, hair_region in hair_results:
        assert isinstance(hair_mask, np.ndarray)
        assert len(hair_region) == 4
        
        # Extract and classify hair pixels
        hair_pixels = detector.extract_hair_pixels(image, hair_mask, hair_region)
        
        if len(hair_pixels) > 0:
            result = classifier.analyze_hair_color_distribution(hair_pixels)
            assert 'primary_color' in result
            assert 'confidence' in result
            assert result['total_pixels'] > 0


if __name__ == "__main__":
    pytest.main([__file__])
