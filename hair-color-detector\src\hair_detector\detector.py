"""Hair Detection Module

This module provides functionality to detect hair regions in images using computer vision techniques.
"""

import cv2
import numpy as np
from typing import Tuple, Optional, List
import logging

logger = logging.getLogger(__name__)


class HairDetector:
    """Hair detection using computer vision techniques."""
    
    def __init__(self, face_cascade_path: Optional[str] = None):
        """Initialize the hair detector.
        
        Args:
            face_cascade_path: Path to face cascade classifier. If None, uses default OpenCV classifier.
        """
        # Load face cascade for face detection
        if face_cascade_path:
            self.face_cascade = cv2.CascadeClassifier(face_cascade_path)
        else:
            # Use default OpenCV face cascade
            self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        
        if self.face_cascade.empty():
            raise ValueError("Could not load face cascade classifier")
    
    def detect_faces(self, image: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """Detect faces in the image.
        
        Args:
            image: Input image as numpy array
            
        Returns:
            List of face rectangles as (x, y, width, height) tuples
        """
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        faces = self.face_cascade.detectMultiScale(
            gray,
            scaleFactor=1.1,
            minNeighbors=5,
            minSize=(30, 30)
        )
        return [(int(x), int(y), int(w), int(h)) for x, y, w, h in faces]
    
    def estimate_hair_region(self, image: np.ndarray, face_rect: Tuple[int, int, int, int]) -> Tuple[int, int, int, int]:
        """Estimate hair region based on detected face.
        
        Args:
            image: Input image
            face_rect: Face rectangle (x, y, width, height)
            
        Returns:
            Hair region rectangle (x, y, width, height)
        """
        x, y, w, h = face_rect
        img_height, img_width = image.shape[:2]
        
        # Hair region is typically above the face
        # Extend upward from face top by face height * 0.8
        hair_height = int(h * 0.8)
        hair_y = max(0, y - hair_height)
        
        # Extend hair region width slightly beyond face width
        hair_width = int(w * 1.2)
        hair_x = max(0, x - int(w * 0.1))
        
        # Ensure we don't exceed image boundaries
        hair_width = min(hair_width, img_width - hair_x)
        hair_height = min(hair_height, y - hair_y)
        
        return (hair_x, hair_y, hair_width, hair_height)
    
    def segment_hair(self, image: np.ndarray, hair_region: Tuple[int, int, int, int]) -> np.ndarray:
        """Segment hair from the estimated hair region using color and texture analysis.
        
        Args:
            image: Input image
            hair_region: Hair region rectangle (x, y, width, height)
            
        Returns:
            Binary mask where hair pixels are white (255) and non-hair pixels are black (0)
        """
        x, y, w, h = hair_region
        roi = image[y:y+h, x:x+w]
        
        if roi.size == 0:
            return np.zeros((h, w), dtype=np.uint8)
        
        # Convert to different color spaces for better hair detection
        hsv = cv2.cvtColor(roi, cv2.COLOR_BGR2HSV)
        lab = cv2.cvtColor(roi, cv2.COLOR_BGR2LAB)
        
        # Create multiple masks for different hair characteristics
        masks = []
        
        # 1. HSV-based mask for typical hair colors
        # Dark hair (black, brown)
        lower_dark = np.array([0, 0, 0])
        upper_dark = np.array([180, 255, 80])
        mask_dark = cv2.inRange(hsv, lower_dark, upper_dark)
        masks.append(mask_dark)
        
        # 2. LAB-based mask for better color discrimination
        # Focus on L channel for brightness and A/B for color
        l_channel = lab[:, :, 0]
        mask_lab = cv2.inRange(l_channel, 0, 120)  # Darker regions
        masks.append(mask_lab)
        
        # 3. Texture-based detection using edge detection
        gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray_roi, 50, 150)
        
        # Dilate edges to capture hair texture
        kernel = np.ones((3, 3), np.uint8)
        edges_dilated = cv2.dilate(edges, kernel, iterations=1)
        masks.append(edges_dilated)
        
        # Combine all masks
        combined_mask = np.zeros_like(masks[0])
        for mask in masks:
            combined_mask = cv2.bitwise_or(combined_mask, mask)
        
        # Apply morphological operations to clean up the mask
        kernel = np.ones((5, 5), np.uint8)
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)
        
        # Apply Gaussian blur to smooth the mask
        combined_mask = cv2.GaussianBlur(combined_mask, (5, 5), 0)
        
        return combined_mask
    
    def detect_hair(self, image: np.ndarray) -> List[Tuple[np.ndarray, Tuple[int, int, int, int]]]:
        """Detect hair regions in the image.
        
        Args:
            image: Input image as numpy array
            
        Returns:
            List of tuples containing (hair_mask, hair_region_rect) for each detected face
        """
        faces = self.detect_faces(image)
        results = []
        
        for face_rect in faces:
            try:
                hair_region = self.estimate_hair_region(image, face_rect)
                hair_mask = self.segment_hair(image, hair_region)
                results.append((hair_mask, hair_region))
            except Exception as e:
                logger.warning(f"Failed to detect hair for face {face_rect}: {e}")
                continue
        
        return results
    
    def extract_hair_pixels(self, image: np.ndarray, hair_mask: np.ndarray, 
                           hair_region: Tuple[int, int, int, int]) -> np.ndarray:
        """Extract hair pixels from the image using the hair mask.
        
        Args:
            image: Input image
            hair_mask: Binary mask indicating hair pixels
            hair_region: Hair region rectangle (x, y, width, height)
            
        Returns:
            Array of hair pixel colors (BGR format)
        """
        x, y, w, h = hair_region
        roi = image[y:y+h, x:x+w]
        
        # Apply mask to extract only hair pixels
        hair_pixels = roi[hair_mask > 0]
        
        return hair_pixels
