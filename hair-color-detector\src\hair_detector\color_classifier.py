"""Hair Color Classification Module

This module provides functionality to classify hair colors from detected hair pixels.
"""

import numpy as np
import cv2
from typing import Dict, List, Tuple, Optional
from sklearn.cluster import KMeans
from collections import Counter
import logging

logger = logging.getLogger(__name__)


class HairColorClassifier:
    """Classify hair colors from hair pixel data."""
    
    # Define hair color ranges in HSV color space
    COLOR_RANGES = {
        'black': {
            'hsv_lower': np.array([0, 0, 0]),
            'hsv_upper': np.array([180, 255, 50]),
            'description': 'Black hair'
        },
        'brown': {
            'hsv_lower': np.array([8, 50, 20]),
            'hsv_upper': np.array([25, 255, 200]),
            'description': 'Brown hair'
        },
        'blonde': {
            'hsv_lower': np.array([15, 30, 100]),
            'hsv_upper': np.array([35, 255, 255]),
            'description': 'Blonde hair'
        },
        'red': {
            'hsv_lower': np.array([0, 50, 50]),
            'hsv_upper': np.array([10, 255, 255]),
            'description': 'Red hair'
        },
        'gray': {
            'hsv_lower': np.array([0, 0, 50]),
            'hsv_upper': np.array([180, 30, 200]),
            'description': 'Gray/Silver hair'
        },
        'white': {
            'hsv_lower': np.array([0, 0, 200]),
            'hsv_upper': np.array([180, 30, 255]),
            'description': 'White hair'
        }
    }
    
    def __init__(self, n_clusters: int = 3):
        """Initialize the hair color classifier.
        
        Args:
            n_clusters: Number of clusters for K-means color analysis
        """
        self.n_clusters = n_clusters
    
    def extract_dominant_colors(self, hair_pixels: np.ndarray) -> List[Tuple[np.ndarray, float]]:
        """Extract dominant colors from hair pixels using K-means clustering.
        
        Args:
            hair_pixels: Array of hair pixel colors (BGR format)
            
        Returns:
            List of (color, percentage) tuples sorted by dominance
        """
        if len(hair_pixels) == 0:
            return []
        
        # Reshape pixels for K-means
        pixels = hair_pixels.reshape(-1, 3)
        
        # Apply K-means clustering
        n_clusters = min(self.n_clusters, len(pixels))
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        kmeans.fit(pixels)
        
        # Get cluster centers and labels
        colors = kmeans.cluster_centers_
        labels = kmeans.labels_
        
        # Calculate percentages
        label_counts = Counter(labels)
        total_pixels = len(pixels)
        
        # Create list of (color, percentage) tuples
        dominant_colors = []
        for i, color in enumerate(colors):
            percentage = label_counts[i] / total_pixels
            dominant_colors.append((color.astype(np.uint8), percentage))
        
        # Sort by percentage (descending)
        dominant_colors.sort(key=lambda x: x[1], reverse=True)
        
        return dominant_colors
    
    def bgr_to_hsv(self, bgr_color: np.ndarray) -> np.ndarray:
        """Convert BGR color to HSV.
        
        Args:
            bgr_color: BGR color array
            
        Returns:
            HSV color array
        """
        # Create a 1x1 image with the color
        bgr_pixel = np.uint8([[bgr_color]])
        hsv_pixel = cv2.cvtColor(bgr_pixel, cv2.COLOR_BGR2HSV)
        return hsv_pixel[0][0]
    
    def classify_color(self, bgr_color: np.ndarray) -> str:
        """Classify a single BGR color into a hair color category.
        
        Args:
            bgr_color: BGR color array
            
        Returns:
            Hair color category name
        """
        hsv_color = self.bgr_to_hsv(bgr_color)
        
        # Check against each color range
        for color_name, color_info in self.COLOR_RANGES.items():
            lower = color_info['hsv_lower']
            upper = color_info['hsv_upper']
            
            # Special handling for red hue (wraps around 0/180)
            if color_name == 'red':
                # Red can be in range [0, 10] or [170, 180]
                in_range1 = np.all(hsv_color >= np.array([0, 50, 50])) and np.all(hsv_color <= np.array([10, 255, 255]))
                in_range2 = np.all(hsv_color >= np.array([170, 50, 50])) and np.all(hsv_color <= np.array([180, 255, 255]))
                if in_range1 or in_range2:
                    return color_name
            else:
                if np.all(hsv_color >= lower) and np.all(hsv_color <= upper):
                    return color_name
        
        # Default classification based on brightness and saturation
        h, s, v = hsv_color
        
        if v < 50:  # Very dark
            return 'black'
        elif s < 30:  # Low saturation
            if v > 200:
                return 'white'
            else:
                return 'gray'
        elif v > 150 and s > 30:  # Bright and saturated
            if h < 25:
                return 'blonde'
            else:
                return 'brown'
        else:
            return 'brown'  # Default to brown
    
    def classify_hair_color(self, hair_pixels: np.ndarray) -> Dict[str, float]:
        """Classify hair color from hair pixels.
        
        Args:
            hair_pixels: Array of hair pixel colors (BGR format)
            
        Returns:
            Dictionary with color categories and their confidence scores
        """
        if len(hair_pixels) == 0:
            return {'unknown': 1.0}
        
        # Extract dominant colors
        dominant_colors = self.extract_dominant_colors(hair_pixels)
        
        if not dominant_colors:
            return {'unknown': 1.0}
        
        # Classify each dominant color
        color_scores = {}
        
        for color, percentage in dominant_colors:
            color_category = self.classify_color(color)
            
            if color_category in color_scores:
                color_scores[color_category] += percentage
            else:
                color_scores[color_category] = percentage
        
        # Normalize scores
        total_score = sum(color_scores.values())
        if total_score > 0:
            color_scores = {k: v / total_score for k, v in color_scores.items()}
        
        return color_scores
    
    def get_primary_hair_color(self, hair_pixels: np.ndarray) -> Tuple[str, float]:
        """Get the primary hair color and confidence score.
        
        Args:
            hair_pixels: Array of hair pixel colors (BGR format)
            
        Returns:
            Tuple of (primary_color, confidence_score)
        """
        color_scores = self.classify_hair_color(hair_pixels)
        
        if not color_scores:
            return ('unknown', 0.0)
        
        # Get the color with highest score
        primary_color = max(color_scores.items(), key=lambda x: x[1])
        
        return primary_color
    
    def get_color_description(self, color_name: str) -> str:
        """Get human-readable description for a color category.
        
        Args:
            color_name: Color category name
            
        Returns:
            Human-readable description
        """
        if color_name in self.COLOR_RANGES:
            return self.COLOR_RANGES[color_name]['description']
        else:
            return f"{color_name.capitalize()} hair"
    
    def analyze_hair_color_distribution(self, hair_pixels: np.ndarray) -> Dict:
        """Perform comprehensive hair color analysis.
        
        Args:
            hair_pixels: Array of hair pixel colors (BGR format)
            
        Returns:
            Dictionary with detailed color analysis
        """
        if len(hair_pixels) == 0:
            return {
                'primary_color': 'unknown',
                'confidence': 0.0,
                'color_distribution': {},
                'dominant_colors': [],
                'total_pixels': 0
            }
        
        # Get color distribution
        color_scores = self.classify_hair_color(hair_pixels)
        primary_color, confidence = self.get_primary_hair_color(hair_pixels)
        dominant_colors = self.extract_dominant_colors(hair_pixels)
        
        # Convert dominant colors to readable format
        dominant_colors_info = []
        for color, percentage in dominant_colors:
            color_category = self.classify_color(color)
            dominant_colors_info.append({
                'bgr_color': color.tolist(),
                'percentage': percentage,
                'category': color_category,
                'description': self.get_color_description(color_category)
            })
        
        return {
            'primary_color': primary_color,
            'confidence': confidence,
            'color_distribution': color_scores,
            'dominant_colors': dominant_colors_info,
            'total_pixels': len(hair_pixels),
            'description': self.get_color_description(primary_color)
        }
