# Hair Color Detector

A Python utility for categorizing images based on hair color detection using computer vision techniques.

## Features

- **Face Detection**: Automatically detects faces in images using OpenCV's Haar cascade classifiers
- **Hair Region Estimation**: Intelligently estimates hair regions based on detected faces
- **Hair Segmentation**: Uses color and texture analysis to segment hair from background
- **Color Classification**: Classifies hair into categories: black, brown, blonde, red, gray, white
- **Batch Processing**: Process multiple images at once
- **Image Organization**: Automatically organize images into color-based directories
- **Detailed Analysis**: Provides confidence scores and color distribution analysis
- **Debug Output**: Generate debug images showing detection results

## Installation

This project uses `uv` for dependency management. Make sure you have `uv` installed.

```bash
# Clone the repository
git clone <repository-url>
cd hair-color-detector

# Install dependencies
uv sync
```

## Usage

### Command Line Interface

The tool provides several commands for different use cases:

#### Quick Demo
```bash
uv run hair-detector demo path/to/image.jpg
```

#### Detailed Analysis
```bash
uv run hair-detector analyze path/to/image.jpg --output results --save-debug
```

#### Batch Processing
```bash
uv run hair-detector batch input_directory output_directory --organize
```

### Python API

```python
from hair_detector import HairDetector, HairColorClassifier
import cv2

# Load image
image = cv2.imread('path/to/image.jpg')

# Initialize detectors
hair_detector = HairDetector()
color_classifier = HairColorClassifier()

# Detect hair regions
hair_results = hair_detector.detect_hair(image)

# Analyze each region
for hair_mask, hair_region in hair_results:
    hair_pixels = hair_detector.extract_hair_pixels(image, hair_mask, hair_region)
    color_analysis = color_classifier.analyze_hair_color_distribution(hair_pixels)

    print(f"Primary color: {color_analysis['description']}")
    print(f"Confidence: {color_analysis['confidence']:.1%}")
```

## Hair Detection Method

The hair detection system uses a multi-step approach:

1. **Face Detection**: Uses OpenCV's Haar cascade classifier to detect faces
2. **Hair Region Estimation**: Estimates hair region above detected faces
3. **Hair Segmentation**: Combines multiple techniques:
   - HSV color space analysis for typical hair colors
   - LAB color space for better color discrimination
   - Edge detection for hair texture analysis
   - Morphological operations for noise reduction
4. **Color Classification**: Uses K-means clustering and predefined color ranges

## Supported Hair Colors

- **Black**: Very dark hair colors
- **Brown**: Medium to dark brown shades
- **Blonde**: Light brown to blonde shades
- **Red**: Red and auburn hair colors
- **Gray**: Gray and silver hair
- **White**: White and very light hair

## Testing

Run the test suite:

```bash
uv run python -m pytest tests/ -v
```

## Requirements

- Python 3.8+
- OpenCV, NumPy, scikit-learn, Pillow, Click, Matplotlib

## Limitations

- Works best with clear, front-facing portraits
- Performance depends on image quality and lighting
- May have difficulty with very unusual hair colors or styles
- Requires visible faces for hair region estimation