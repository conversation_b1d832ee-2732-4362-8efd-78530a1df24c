# Technology Stack

## Core Technologies
- **Python 3.8+**: Primary programming language
- **uv**: Modern Python package manager and environment management

## Computer Vision & Image Processing
- **OpenCV**: Face detection, image processing, color space conversions
- **NumPy**: Numerical operations and array handling
- **Pillow (PIL)**: Image loading and basic processing

## Machine Learning
- **scikit-learn**: K-means clustering for color analysis

## CLI & User Interface
- **Click**: Command-line interface framework

## Visualization
- **Matplotlib**: Color visualization and plotting

## Testing
- **pytest**: Unit testing framework
- **pytest-cov**: Test coverage reporting

## Build System
- **Hatchling**: Modern Python build backend

## Project Structure
- **src/**: Source code in modern Python package layout
- **tests/**: Unit tests and validation
- **sample_images/**: Test images for validation
