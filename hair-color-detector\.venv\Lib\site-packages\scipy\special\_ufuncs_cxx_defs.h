#ifndef UFUNCS_PROTO_H
#define UFUNCS_PROTO_H 1
#include "boost_special_functions.h"
npy_float beta_pdf_float(npy_float, npy_float, npy_float);
npy_double beta_pdf_double(npy_double, npy_double, npy_double);
npy_float beta_ppf_float(npy_float, npy_float, npy_float);
npy_double beta_ppf_double(npy_double, npy_double, npy_double);
npy_float binom_cdf_float(npy_float, npy_float, npy_float);
npy_double binom_cdf_double(npy_double, npy_double, npy_double);
npy_float binom_isf_float(npy_float, npy_float, npy_float);
npy_double binom_isf_double(npy_double, npy_double, npy_double);
npy_float binom_pmf_float(npy_float, npy_float, npy_float);
npy_double binom_pmf_double(npy_double, npy_double, npy_double);
npy_float binom_ppf_float(npy_float, npy_float, npy_float);
npy_double binom_ppf_double(npy_double, npy_double, npy_double);
npy_float binom_sf_float(npy_float, npy_float, npy_float);
npy_double binom_sf_double(npy_double, npy_double, npy_double);
npy_float cauchy_isf_float(npy_float, npy_float, npy_float);
npy_double cauchy_isf_double(npy_double, npy_double, npy_double);
npy_float cauchy_ppf_float(npy_float, npy_float, npy_float);
npy_double cauchy_ppf_double(npy_double, npy_double, npy_double);
npy_float hypergeom_cdf_float(npy_float, npy_float, npy_float, npy_float);
npy_double hypergeom_cdf_double(npy_double, npy_double, npy_double, npy_double);
npy_float hypergeom_mean_float(npy_float, npy_float, npy_float);
npy_double hypergeom_mean_double(npy_double, npy_double, npy_double);
npy_float hypergeom_pmf_float(npy_float, npy_float, npy_float, npy_float);
npy_double hypergeom_pmf_double(npy_double, npy_double, npy_double, npy_double);
npy_float hypergeom_sf_float(npy_float, npy_float, npy_float, npy_float);
npy_double hypergeom_sf_double(npy_double, npy_double, npy_double, npy_double);
npy_float hypergeom_skewness_float(npy_float, npy_float, npy_float);
npy_double hypergeom_skewness_double(npy_double, npy_double, npy_double);
npy_float hypergeom_variance_float(npy_float, npy_float, npy_float);
npy_double hypergeom_variance_double(npy_double, npy_double, npy_double);
npy_float invgauss_isf_float(npy_float, npy_float, npy_float);
npy_double invgauss_isf_double(npy_double, npy_double, npy_double);
npy_float invgauss_ppf_float(npy_float, npy_float, npy_float);
npy_double invgauss_ppf_double(npy_double, npy_double, npy_double);
npy_float landau_cdf_float(npy_float, npy_float, npy_float);
npy_double landau_cdf_double(npy_double, npy_double, npy_double);
npy_float landau_isf_float(npy_float, npy_float, npy_float);
npy_double landau_isf_double(npy_double, npy_double, npy_double);
npy_float landau_pdf_float(npy_float, npy_float, npy_float);
npy_double landau_pdf_double(npy_double, npy_double, npy_double);
npy_float landau_ppf_float(npy_float, npy_float, npy_float);
npy_double landau_ppf_double(npy_double, npy_double, npy_double);
npy_float landau_sf_float(npy_float, npy_float, npy_float);
npy_double landau_sf_double(npy_double, npy_double, npy_double);
npy_float nbinom_cdf_float(npy_float, npy_float, npy_float);
npy_double nbinom_cdf_double(npy_double, npy_double, npy_double);
npy_float nbinom_isf_float(npy_float, npy_float, npy_float);
npy_double nbinom_isf_double(npy_double, npy_double, npy_double);
npy_float nbinom_kurtosis_excess_float(npy_float, npy_float);
npy_double nbinom_kurtosis_excess_double(npy_double, npy_double);
npy_float nbinom_mean_float(npy_float, npy_float);
npy_double nbinom_mean_double(npy_double, npy_double);
npy_float nbinom_pmf_float(npy_float, npy_float, npy_float);
npy_double nbinom_pmf_double(npy_double, npy_double, npy_double);
npy_float nbinom_ppf_float(npy_float, npy_float, npy_float);
npy_double nbinom_ppf_double(npy_double, npy_double, npy_double);
npy_float nbinom_sf_float(npy_float, npy_float, npy_float);
npy_double nbinom_sf_double(npy_double, npy_double, npy_double);
npy_float nbinom_skewness_float(npy_float, npy_float);
npy_double nbinom_skewness_double(npy_double, npy_double);
npy_float nbinom_variance_float(npy_float, npy_float);
npy_double nbinom_variance_double(npy_double, npy_double);
npy_float ncf_isf_float(npy_float, npy_float, npy_float, npy_float);
npy_double ncf_isf_double(npy_double, npy_double, npy_double, npy_double);
npy_float ncf_kurtosis_excess_float(npy_float, npy_float, npy_float);
npy_double ncf_kurtosis_excess_double(npy_double, npy_double, npy_double);
npy_float ncf_mean_float(npy_float, npy_float, npy_float);
npy_double ncf_mean_double(npy_double, npy_double, npy_double);
npy_float ncf_pdf_float(npy_float, npy_float, npy_float, npy_float);
npy_double ncf_pdf_double(npy_double, npy_double, npy_double, npy_double);
npy_float ncf_sf_float(npy_float, npy_float, npy_float, npy_float);
npy_double ncf_sf_double(npy_double, npy_double, npy_double, npy_double);
npy_float ncf_skewness_float(npy_float, npy_float, npy_float);
npy_double ncf_skewness_double(npy_double, npy_double, npy_double);
npy_float ncf_variance_float(npy_float, npy_float, npy_float);
npy_double ncf_variance_double(npy_double, npy_double, npy_double);
npy_float nct_isf_float(npy_float, npy_float, npy_float);
npy_double nct_isf_double(npy_double, npy_double, npy_double);
npy_float nct_kurtosis_excess_float(npy_float, npy_float);
npy_double nct_kurtosis_excess_double(npy_double, npy_double);
npy_float nct_mean_float(npy_float, npy_float);
npy_double nct_mean_double(npy_double, npy_double);
npy_float nct_pdf_float(npy_float, npy_float, npy_float);
npy_double nct_pdf_double(npy_double, npy_double, npy_double);
npy_float nct_sf_float(npy_float, npy_float, npy_float);
npy_double nct_sf_double(npy_double, npy_double, npy_double);
npy_float nct_skewness_float(npy_float, npy_float);
npy_double nct_skewness_double(npy_double, npy_double);
npy_float nct_variance_float(npy_float, npy_float);
npy_double nct_variance_double(npy_double, npy_double);
npy_float ncx2_cdf_float(npy_float, npy_float, npy_float);
npy_double ncx2_cdf_double(npy_double, npy_double, npy_double);
npy_float ncx2_isf_float(npy_float, npy_float, npy_float);
npy_double ncx2_isf_double(npy_double, npy_double, npy_double);
npy_float ncx2_pdf_float(npy_float, npy_float, npy_float);
npy_double ncx2_pdf_double(npy_double, npy_double, npy_double);
npy_float ncx2_ppf_float(npy_float, npy_float, npy_float);
npy_double ncx2_ppf_double(npy_double, npy_double, npy_double);
npy_float ncx2_sf_float(npy_float, npy_float, npy_float);
npy_double ncx2_sf_double(npy_double, npy_double, npy_double);
npy_float skewnorm_cdf_float(npy_float, npy_float, npy_float, npy_float);
npy_double skewnorm_cdf_double(npy_double, npy_double, npy_double, npy_double);
npy_float skewnorm_isf_float(npy_float, npy_float, npy_float, npy_float);
npy_double skewnorm_isf_double(npy_double, npy_double, npy_double, npy_double);
npy_float skewnorm_ppf_float(npy_float, npy_float, npy_float, npy_float);
npy_double skewnorm_ppf_double(npy_double, npy_double, npy_double, npy_double);
#include "stirling2.h"
npy_double _stirling2_inexact(npy_double, npy_double);
npy_float ibeta_float(npy_float, npy_float, npy_float);
npy_double ibeta_double(npy_double, npy_double, npy_double);
npy_float ibetac_float(npy_float, npy_float, npy_float);
npy_double ibetac_double(npy_double, npy_double, npy_double);
npy_float ibetac_inv_float(npy_float, npy_float, npy_float);
npy_double ibetac_inv_double(npy_double, npy_double, npy_double);
npy_float ibeta_inv_float(npy_float, npy_float, npy_float);
npy_double ibeta_inv_double(npy_double, npy_double, npy_double);
#include "ellint_carlson_wrap.hh"
npy_double fellint_RC(npy_double, npy_double);
npy_cdouble cellint_RC(npy_cdouble, npy_cdouble);
npy_double fellint_RD(npy_double, npy_double, npy_double);
npy_cdouble cellint_RD(npy_cdouble, npy_cdouble, npy_cdouble);
npy_double fellint_RF(npy_double, npy_double, npy_double);
npy_cdouble cellint_RF(npy_cdouble, npy_cdouble, npy_cdouble);
npy_double fellint_RG(npy_double, npy_double, npy_double);
npy_cdouble cellint_RG(npy_cdouble, npy_cdouble, npy_cdouble);
npy_double fellint_RJ(npy_double, npy_double, npy_double, npy_double);
npy_cdouble cellint_RJ(npy_cdouble, npy_cdouble, npy_cdouble, npy_cdouble);
npy_float erfinv_float(npy_float);
npy_double erfinv_double(npy_double);
npy_double hyp1f1_double(npy_double, npy_double, npy_double);
npy_float ncf_cdf_float(npy_float, npy_float, npy_float, npy_float);
npy_double ncf_cdf_double(npy_double, npy_double, npy_double, npy_double);
npy_float ncf_ppf_float(npy_float, npy_float, npy_float, npy_float);
npy_double ncf_ppf_double(npy_double, npy_double, npy_double, npy_double);
npy_float nct_cdf_float(npy_float, npy_float, npy_float);
npy_double nct_cdf_double(npy_double, npy_double, npy_double);
npy_float nct_ppf_float(npy_float, npy_float, npy_float);
npy_double nct_ppf_double(npy_double, npy_double, npy_double);
npy_float powm1_float(npy_float, npy_float);
npy_double powm1_double(npy_double, npy_double);
#include "_wright.h"
npy_cdouble wrightomega(npy_cdouble);
npy_double wrightomega_real(npy_double);
#endif
